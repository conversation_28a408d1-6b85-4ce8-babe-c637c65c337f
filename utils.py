import pandas as pd
from io import BytesIO

def read_excel_file(uploaded_file):
    """Reads an uploaded Excel file into a pandas DataFrame."""
    df = pd.read_excel(uploaded_file)
    if 'CompanyName' not in df.columns:
        # If 'CompanyName' is not present, we assume the Chinese name and rename it.
        if '企业名称' in df.columns:
            df.rename(columns={'企业名称': 'CompanyName'}, inplace=True)
        else:
            raise ValueError("Excel file must contain a 'CompanyName' or '企业名称' column.")
    return df

def read_excel_from_path(file_path: str) -> pd.DataFrame:
    """Reads an Excel file from a given path into a pandas DataFrame."""
    df = pd.read_excel(file_path)
    
    # Map Chinese columns to English for easier processing
    column_mapping = {
        '企业名称': 'CompanyName',
        '邮箱': 'ExistingEmail', 
        '网址': 'ExistingWebsite',
        '企业地址': 'ExistingAddress',
        '法定代表人': 'LegalRepresentative',
        '注册资本': 'RegisteredCapital',
        '成立日期': 'EstablishmentDate',
        '所属城市': 'City',
        '所属省份': 'Province',
        '所属区县': 'District',
        '登记状态': 'RegistrationStatus',
        '企业类型': 'CompanyType',
        '经营范围': 'BusinessScope',
        '统一社会信用代码': 'CreditCode'
    }
    
    # Rename columns that exist
    for chinese_col, english_col in column_mapping.items():
        if chinese_col in df.columns:
            df.rename(columns={chinese_col: english_col}, inplace=True)
    
    # Ensure CompanyName exists
    if 'CompanyName' not in df.columns:
        df.rename(columns={df.columns[0]: 'CompanyName'}, inplace=True)
    
    return df

def save_to_excel(df: pd.DataFrame) -> bytes:
    """Saves a DataFrame to an in-memory Excel file and returns the bytes."""
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Contacts')
    return output.getvalue()
