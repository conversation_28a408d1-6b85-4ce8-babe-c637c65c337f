import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import random
import re
from typing import Dict, List, Optional, Tuple
from pandas import DataFrame
import os
from urllib.parse import urlparse
from fake_useragent import UserAgent
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from playwright.sync_api import sync_playwright
import chardet
import urllib.parse
import socket
import functools
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# DNS Resolution Improvements for Chinese Websites
try:
    import dns.resolver
    DNS_AVAILABLE = True
    logging.info("✅ DNS optimization available")
except ImportError:
    DNS_AVAILABLE = False
    logging.warning("⚠️ dnspython not available - DNS optimization disabled")

# Configure Chinese logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('scraper.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Add Chinese translations
class ChineseFilter(logging.Filter):
    translations = {
        'DEBUG': '调试',
        'INFO': '信息',
        'WARNING': '警告',
        'ERROR': '错误',
        'CRITICAL': '严重'
    }
    
    def filter(self, record):
        if record.levelname in self.translations:
            record.levelname = self.translations[record.levelname]
        return True

logger.addFilter(ChineseFilter())

# --- DNS Configuration for Chinese Websites ---

CHINESE_DNS_SERVERS = [
    '***************',  # China Telecom
    '*********',        # Alibaba DNS
    '************',     # DNSPod
    '************',     # Baidu DNS
    '*******',          # Google DNS (fallback)
    '*******'           # Cloudflare DNS (fallback)
]

def setup_dns_for_china():
    """Configure DNS settings for Chinese websites to resolve NameResolutionError."""
    if not DNS_AVAILABLE:
        logger.warning("DNS optimization not available - install dnspython: pip install dnspython")
        return False

    try:
        # Create custom resolver with Chinese DNS servers
        resolver = dns.resolver.Resolver()
        resolver.nameservers = CHINESE_DNS_SERVERS[:4]  # Use Chinese DNS servers
        resolver.timeout = 10
        resolver.lifetime = 30

        # Cache for DNS lookups
        dns_cache = {}

        # Override socket.getaddrinfo to use our resolver
        original_getaddrinfo = socket.getaddrinfo

        @functools.lru_cache(maxsize=1024)
        def custom_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
            try:
                # Skip if it's already an IP address
                if host.replace('.', '').replace(':', '').isdigit():
                    return original_getaddrinfo(host, port, family, type, proto, flags)

                # Check cache first
                if host in dns_cache:
                    ip = dns_cache[host]
                    return original_getaddrinfo(ip, port, family, type, proto, flags)

                # Try to resolve with Chinese DNS servers
                try:
                    answers = resolver.resolve(host, 'A')
                    ip = str(answers[0])
                    dns_cache[host] = ip
                    logger.debug(f"🌐 DNS resolved {host} -> {ip}")
                    return original_getaddrinfo(ip, port, family, type, proto, flags)
                except Exception as dns_error:
                    logger.debug(f"Chinese DNS failed for {host}: {dns_error}")
                    # Try with fallback DNS servers
                    for fallback_dns in CHINESE_DNS_SERVERS[4:]:
                        try:
                            fallback_resolver = dns.resolver.Resolver()
                            fallback_resolver.nameservers = [fallback_dns]
                            answers = fallback_resolver.resolve(host, 'A')
                            ip = str(answers[0])
                            dns_cache[host] = ip
                            logger.debug(f"🌐 Fallback DNS resolved {host} -> {ip}")
                            return original_getaddrinfo(ip, port, family, type, proto, flags)
                        except:
                            continue

                    # If all DNS servers fail, use system DNS
                    raise dns_error

            except Exception as e:
                logger.debug(f"Custom DNS failed for {host}: {e}, using system DNS")
                return original_getaddrinfo(host, port, family, type, proto, flags)

        socket.getaddrinfo = custom_getaddrinfo
        logger.info("✅ Chinese DNS configuration applied successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to configure DNS: {e}")
        return False

def try_multiple_dns_servers(domain: str) -> Optional[str]:
    """Try multiple DNS servers to resolve a domain."""
    if not DNS_AVAILABLE:
        return None

    for dns_server in CHINESE_DNS_SERVERS:
        try:
            resolver = dns.resolver.Resolver()
            resolver.nameservers = [dns_server]
            resolver.timeout = 5
            answers = resolver.resolve(domain, 'A')
            ip = str(answers[0])
            logger.debug(f"🌐 Resolved {domain} -> {ip} using {dns_server}")
            return ip
        except Exception as e:
            logger.debug(f"DNS server {dns_server} failed for {domain}: {e}")
            continue

    logger.warning(f"⚠️ All DNS servers failed for {domain}")
    return None

# Initialize DNS configuration
dns_configured = setup_dns_for_china()
if dns_configured:
    logger.info("🚀 Enhanced DNS configuration loaded for Chinese websites")
else:
    logger.warning("⚠️ Using system DNS - may have issues with Chinese websites")

# --- Session Management ---

class BrowserSession:
    """Advanced stealth browser session optimized for Chinese websites."""

    # Chinese-specific user agents
    CHINESE_USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36"
    ]

    def __init__(self, proxy: Optional[Tuple[str, int]] = None):
        self.playwright_available = True
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.last_request_time = 0
        self.min_delay = 2.0
        self.max_delay = 8.0  # Longer delays for Chinese sites

        try:
            self.playwright = sync_playwright().start()
            self._create_stealth_browser(proxy)
        except Exception as e:
            self.playwright_available = False
            logger.warning("Playwright初始化失败，将使用基本请求模式: %s", str(e))

    def _create_stealth_browser(self, proxy: Optional[Tuple[str, int]] = None):
        """Create a stealth browser with advanced anti-detection."""
        proxy_config = None
        if proxy:
            proxy_config = {
                'server': f"http://{proxy[0]}:{proxy[1]}",
                'username': proxy[2] if len(proxy) > 2 else None,
                'password': proxy[3] if len(proxy) > 3 else None
            }

        # Advanced browser launch arguments for stealth
        self.browser = self.playwright.chromium.launch(
            headless=True,
            proxy=proxy_config,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',  # Faster loading
                '--disable-javascript-harmony-shipping',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ],
            timeout=60000
        )

        # Create stealth context with Chinese locale
        user_agent = random.choice(self.CHINESE_USER_AGENTS)
        self.context = self.browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent=user_agent,
            locale="zh-CN",
            timezone_id="Asia/Shanghai",
            geolocation={"longitude": 116.3683244, "latitude": 39.915085},  # Beijing coordinates
            permissions=["geolocation"],
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br',
                'Referer': 'https://www.baidu.com/',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
        )

        # Add stealth scripts to override detection
        self.context.add_init_script("""
            // Override webdriver detection
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});

            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });

            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en']
            });

            // Override chrome detection
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };

            // Override permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // Override WebGL
            const getParameter = WebGLRenderingContext.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) {
                    return 'Intel Inc.';
                }
                if (parameter === 37446) {
                    return 'Intel(R) Iris(TM) Graphics 6100';
                }
                return getParameter(parameter);
            };
        """)

        self.page = self.context.new_page()
    
    def _rate_limit(self):
        """Implement rate limiting between requests with longer delays for Chinese sites."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        delay = random.uniform(self.min_delay, self.max_delay)

        if time_since_last < delay:
            time.sleep(delay - time_since_last)
        self.last_request_time = time.time()

    def _human_like_interaction(self):
        """Simulate human-like behavior on the page."""
        try:
            # Random mouse movements
            for _ in range(random.randint(3, 7)):
                x = random.randint(100, 1200)
                y = random.randint(100, 800)
                self.page.mouse.move(x, y)
                time.sleep(random.uniform(0.1, 0.5))

            # Random scrolling
            scroll_steps = random.randint(3, 8)
            for _ in range(scroll_steps):
                scroll_amount = random.randint(200, 800)
                self.page.evaluate(f"window.scrollBy(0, {scroll_amount})")
                time.sleep(random.uniform(0.3, 1.2))

            # Random clicks on non-interactive elements (occasionally)
            if random.random() > 0.7:
                try:
                    self.page.mouse.click(500, 300)
                    time.sleep(random.uniform(0.5, 1.5))
                except:
                    pass  # Ignore click failures

            # Simulate reading time
            time.sleep(random.uniform(1.0, 3.0))

        except Exception as e:
            logger.debug(f"Human-like interaction failed: {e}")

    def get(self, url: str) -> str:
        """Navigate to URL with stealth techniques and return rendered HTML."""
        self._rate_limit()
        try:
            if not self.playwright_available:
                logger.warning("Playwright不可用，无法使用浏览器模式")
                raise Exception("Playwright不可用")

            # Navigate with wait for network idle
            self.page.goto(url, timeout=60000, wait_until="networkidle")

            # Simulate human-like behavior
            self._human_like_interaction()

            # Check for various CAPTCHA indicators
            if self._handle_captcha():
                time.sleep(random.uniform(2.0, 5.0))

            # Additional wait for dynamic content
            time.sleep(random.uniform(2.0, 5.0))

            content = self.page.content()
            return content

        except Exception as e:
            logger.error("浏览器导航失败: %s", str(e))
            raise

    def _handle_captcha(self) -> bool:
        """Enhanced CAPTCHA detection and handling."""
        try:
            # Multiple CAPTCHA detection methods
            captcha_indicators = [
                "#captcha", ".captcha", "[id*='captcha']", "[class*='captcha']",
                "#verify", ".verify", "[id*='verify']", "[class*='verify']",
                ".geetest", "#geetest", ".slider", "#slider",
                "[id*='validation']", "[class*='validation']"
            ]

            # Check URL for CAPTCHA indicators
            current_url = self.page.url.lower()
            if any(indicator in current_url for indicator in ["captcha", "verify", "challenge", "robot"]):
                logger.warning("CAPTCHA detected in URL - consider using CAPTCHA solving service")
                return True

            # Check page content for CAPTCHA elements
            for selector in captcha_indicators:
                if self.page.query_selector(selector):
                    logger.warning(f"CAPTCHA detected with selector: {selector}")
                    return True

            # Check for common CAPTCHA text
            page_text = self.page.evaluate("document.body.innerText").lower()
            captcha_keywords = ["验证码", "captcha", "verification", "robot", "人机验证", "滑动验证"]
            if any(keyword in page_text for keyword in captcha_keywords):
                logger.warning("CAPTCHA detected in page text")
                return True

            return False

        except Exception as e:
            logger.debug(f"CAPTCHA detection failed: {e}")
            return False
    
    def close(self):
        """Close browser resources."""
        self.page.close()
        self.context.close()
        self.browser.close()
        self.playwright.stop()

class ScrapingSession:
    """Enhanced HTTP session optimized for Chinese websites."""

    # Chinese-optimized user agents
    CHINESE_USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0"
    ]

    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.last_request_time = 0
        self.min_delay = 1.5  # Optimized delays for better throughput
        self.max_delay = 4.0
        self.request_count = 0
        self.max_requests_per_session = 50  # Rotate session after 50 requests
        self.failed_requests = 0
        self.max_failed_requests = 5  # Reset session after 5 failures
        self._setup_session()

    def _setup_session(self):
        """Configure session with Chinese-optimized headers."""
        user_agent = random.choice(self.CHINESE_USER_AGENTS)
        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://www.baidu.com/',
            'DNT': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Cache-Control': 'max-age=0'
        })

        # Set session timeout and retry strategy
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        # Enhanced retry strategy for 90% success rate
        retry_strategy = Retry(
            total=5,  # More retries
            backoff_factor=2,  # Exponential backoff
            status_forcelist=[429, 500, 502, 503, 504, 403, 408],  # More status codes
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=20, pool_maxsize=20)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Enhanced GET request with DNS resolution and advanced error handling for 90% success rate."""
        self._rate_limit()

        # Check if session needs refresh
        if self.request_count >= self.max_requests_per_session or self.failed_requests >= self.max_failed_requests:
            logger.info("Refreshing session due to limits")
            self._refresh_session()

        # DNS PRE-RESOLUTION for Chinese websites
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain = parsed_url.netloc

            # Try to pre-resolve DNS for Chinese domains
            if any(tld in domain for tld in ['.cn', '.com.cn', 'qichacha', 'tianyancha', 'aiqicha']):
                resolved_ip = try_multiple_dns_servers(domain)
                if resolved_ip:
                    logger.debug(f"🌐 Pre-resolved {domain} -> {resolved_ip}")
        except Exception as dns_error:
            logger.debug(f"DNS pre-resolution failed: {dns_error}")

        try:
            # Rotate user agent more frequently for better stealth
            if random.random() < 0.5:  # 50% chance to rotate
                self.session.headers['User-Agent'] = random.choice(self.CHINESE_USER_AGENTS)

            # Add random referer for better stealth
            referers = [
                'https://www.baidu.com/',
                'https://www.sogou.com/',
                'https://cn.bing.com/',
                'https://www.google.com.hk/'
            ]
            self.session.headers['Referer'] = random.choice(referers)

            # Set enhanced timeout
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 45

            self.request_count += 1
            response = self.session.get(url, **kwargs)

            # Check for common blocking indicators
            if self._is_blocked_response(response):
                logger.warning(f"Detected blocking for {url}, refreshing session")
                self._refresh_session()
                raise requests.RequestException("Response appears to be blocked")

            response.raise_for_status()
            self.failed_requests = 0  # Reset failure count on success
            return response

        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as dns_error:
            # Handle DNS-specific errors
            logger.warning(f"🌐 DNS/Connection error for {url}: {dns_error}")

            # Try alternative approach for DNS issues
            if "NameResolutionError" in str(dns_error) or "getaddrinfo failed" in str(dns_error):
                logger.info(f"🔄 Attempting DNS resolution workaround for {url}")
                try:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(url)
                    domain = parsed_url.netloc

                    # Try to resolve with our custom DNS
                    resolved_ip = try_multiple_dns_servers(domain)
                    if resolved_ip:
                        # Replace domain with IP in URL
                        new_url = url.replace(domain, resolved_ip)
                        # Add Host header to maintain proper routing
                        kwargs['headers'] = kwargs.get('headers', {})
                        kwargs['headers']['Host'] = domain

                        logger.info(f"🌐 Retrying with resolved IP: {new_url}")
                        response = self.session.get(new_url, **kwargs)
                        response.raise_for_status()
                        self.failed_requests = 0
                        return response
                except Exception as retry_error:
                    logger.error(f"DNS workaround failed: {retry_error}")

            self.failed_requests += 1
            raise

        except requests.RequestException as e:
            self.failed_requests += 1
            logger.warning(f"Request failed for {url} (attempt {self.failed_requests}): {e}")
            raise
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if time_since_last < delay:
            sleep_time = delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _is_blocked_response(self, response: requests.Response) -> bool:
        """Check if response indicates blocking or captcha."""
        if response.status_code in [403, 429]:
            return True

        # Check for common blocking indicators in content
        content_lower = response.text.lower()
        blocking_indicators = [
            '验证码', 'captcha', '人机验证', '访问频繁',
            'access denied', 'blocked', '请稍后再试',
            'robot', '机器人', 'security check'
        ]

        return any(indicator in content_lower for indicator in blocking_indicators)

    def _refresh_session(self):
        """Refresh session to avoid detection."""
        self.session.close()
        self.session = requests.Session()
        self.request_count = 0
        self.failed_requests = 0
        self._setup_session()
        logger.info("Session refreshed successfully")

    def rotate_user_agent(self):
        """Rotate user agent for the session."""
        self.session.headers['User-Agent'] = self.ua.random

# --- Enhanced Chinese Contact Extraction ---

def extract_chinese_contacts(text: str) -> Dict[str, set]:
    """Advanced Chinese-specific contact extraction with comprehensive patterns."""
    contacts = {"emails": set(), "phones": set(), "wechats": set(), "qqs": set()}

    # Enhanced Chinese phone number patterns
    phone_patterns = [
        r'1[3-9]\d{9}',  # Mobile phones (11 digits starting with 1)
        r'0\d{2,3}-?\d{7,8}',  # Landlines with area code
        r'\(\d{3,4}\)\d{7,8}',  # Landlines with parentheses
        r'400-?\d{3}-?\d{4}',  # 400 service numbers
        r'800-?\d{3}-?\d{4}',  # 800 service numbers
        r'\+86\s?1[3-9]\d{9}',  # International format mobile
        r'\+86\s?0\d{2,3}-?\d{7,8}',  # International format landline
        r'(\+?86\s?)?1[3-9]\d{9}',  # Mobile with optional +86
        r'021-?\d{8}',  # Shanghai specific
        r'010-?\d{8}',  # Beijing specific
        r'0755-?\d{8}',  # Shenzhen specific
        r'020-?\d{8}',  # Guangzhou specific
    ]

    # WeChat ID patterns
    wechat_patterns = [
        r'微信[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'WeChat[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'微信号[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'wechat[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
        r'wx[：:\s]*([a-zA-Z][a-zA-Z0-9_-]{5,19})',
    ]

    # QQ number patterns
    qq_patterns = [
        r'QQ[：:\s]*([1-9][0-9]{4,10})',
        r'qq[：:\s]*([1-9][0-9]{4,10})',
        r'企鹅[：:\s]*([1-9][0-9]{4,10})',
        r'扣扣[：:\s]*([1-9][0-9]{4,10})',
    ]

    # Enhanced email patterns
    email_patterns = [
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
        r'邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'电子邮箱[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'Email[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'email[：:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
    ]

    # Extract phones
    for pattern in phone_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            # Clean phone number
            phone = re.sub(r'[-\s+()（）]', '', str(match))
            # Remove country code if present
            if phone.startswith('86') and len(phone) > 11:
                phone = phone[2:]
            elif phone.startswith('+86'):
                phone = phone[3:]

            # Validate phone number length and format
            if len(phone) >= 7 and phone.isdigit():
                if (len(phone) == 11 and phone.startswith('1')) or \
                   (len(phone) >= 10 and phone.startswith('0')) or \
                   (len(phone) >= 10 and phone.startswith(('400', '800'))):
                    contacts["phones"].add(phone)

    # Extract WeChat IDs
    for pattern in wechat_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        contacts["wechats"].update(matches)

    # Extract QQ numbers
    for pattern in qq_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        contacts["qqs"].update(matches)

    # Extract emails
    for pattern in email_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                email = match[0] if match[0] else match[1] if len(match) > 1 else ''
            else:
                email = match

            email = str(email).strip()
            if '@' in email and '.' in email and len(email) > 5:
                # Filter out invalid domains
                skip_domains = ['example.com', 'test.com', 'placeholder', 'noreply', 'no-reply',
                               'dummy', 'localhost', '127.0.0.1', 'tempmail', 'throwaway']
                if not any(skip in email.lower() for skip in skip_domains):
                    contacts["emails"].add(email)

    return contacts

# --- Content Quality Validation ---

def _is_valid_content(response: requests.Response) -> bool:
    """Validate response content quality for 90% success rate."""
    # Check response size
    if len(response.content) < 500:  # Too small to be useful
        logger.debug("Content too small")
        return False

    # Check for error indicators
    content_lower = response.text.lower()
    error_indicators = [
        '404', '403', '500', 'not found', 'access denied',
        'error', '错误', '页面不存在', '访问被拒绝',
        'captcha', '验证码', '人机验证'
    ]

    if any(indicator in content_lower for indicator in error_indicators):
        logger.debug("Error indicators found in content")
        return False

    return True

def _is_valid_html(soup: BeautifulSoup) -> bool:
    """Validate HTML structure quality."""
    # Check if we have meaningful content
    text_content = soup.get_text().strip()
    if len(text_content) < 100:  # Too little text content
        return False

    # Check for Chinese content (good indicator for Chinese business sites)
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text_content)
    if len(chinese_chars) < 10:  # Should have some Chinese content
        logger.debug("Insufficient Chinese content")
        return False

    return True

def _has_business_indicators(content: str) -> bool:
    """Check if content has business-related indicators."""
    business_keywords = [
        '公司', '企业', '有限', '股份', '集团', '联系', '电话', '邮箱',
        '地址', '官网', '网站', '业务', '服务', '产品', '关于我们'
    ]

    content_lower = content.lower()
    found_keywords = sum(1 for keyword in business_keywords if keyword in content_lower)

    return found_keywords >= 3  # Should have at least 3 business indicators

# --- Extraction Functions ---

def extract_from_business_directory(soup: BeautifulSoup, url: str) -> Dict[str, str]:
    """Enhanced extraction from Chinese business directories using advanced patterns."""
    try:
        content = ''

        # COMPREHENSIVE SELECTORS for 90% success rate
        if 'qichacha.com' in url:
            selectors = [
                '.company-info', '.content', '.baseinfo', '.company-header', '.detail-content',
                '.company-detail', '.base-info-wrap', '.contact-info', '.company-base-info',
                '.company-contact', '.base-info-item', '.info-row', '.detail-item',
                '.company-main-info', '.enterprise-info', '.basic-info'
            ]
        elif 'tianyancha.com' in url:
            selectors = [
                '.company-info', '.detail', '.company-header', '.base-info', '.content-wrap',
                '.company-detail-box', '.base-info-content', '.contact-wrap', '.company-base',
                '.enterprise-detail', '.company-contact-info', '.base-detail', '.info-content',
                '.company-main', '.detail-content', '.enterprise-base-info'
            ]
        elif 'aiqicha.com' in url:
            selectors = [
                '.company-info', '.detail-info', '.base-info', '.contact-info', '.company-detail',
                '.enterprise-info', '.basic-info', '.company-base-info', '.detail-content',
                '.info-item', '.company-contact', '.base-detail'
            ]
        elif 'qcc.com' in url:
            selectors = [
                '.company-info', '.detail-info', '.base-info', '.contact-info', '.company-detail',
                '.enterprise-detail', '.basic-info', '.company-base', '.detail-content'
            ]
        else:
            # Generic selectors for other sites
            selectors = [
                '.company-info', '.content', '.detail', '.base-info', '.contact',
                '.about', '.contact-us', '.company-detail', '.info', '.main-content',
                '.enterprise-info', '.business-info', '.company-profile'
            ]

        # Try to find content using selectors
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                content = element.get_text()
                break

        if not content:
            content = soup.get_text()

        # Use enhanced Chinese contact extraction
        extracted_contacts = extract_chinese_contacts(content)

        # Convert to expected format
        results = {
            'ScrapedPhone': 'N/A',
            'ScrapedEmail': 'N/A',
            'ScrapedAddress': 'N/A'
        }

        # Process phones
        if extracted_contacts['phones']:
            results['ScrapedPhone'] = ', '.join(sorted(extracted_contacts['phones']))

        # Process emails
        if extracted_contacts['emails']:
            results['ScrapedEmail'] = ', '.join(sorted(extracted_contacts['emails']))

        # Add WeChat and QQ to phone field if found
        additional_contacts = []
        if extracted_contacts['wechats']:
            wechat_list = list(extracted_contacts['wechats'])[:3]  # Limit to 3
            additional_contacts.extend([f"微信:{wc}" for wc in wechat_list])

        if extracted_contacts['qqs']:
            qq_list = list(extracted_contacts['qqs'])[:2]  # Limit to 2
            additional_contacts.extend([f"QQ:{qq}" for qq in qq_list])

        if additional_contacts:
            if results['ScrapedPhone'] != 'N/A':
                results['ScrapedPhone'] += ', ' + ', '.join(additional_contacts)
            else:
                results['ScrapedPhone'] = ', '.join(additional_contacts)

        # Enhanced address extraction using traditional patterns as fallback
        if results['ScrapedAddress'] == 'N/A':
            address_patterns = [
                r'地址[：:\s]*([^\n]+)',
                r'注册地址[：:\s]*([^\n]+)',
                r'办公地址[：:\s]*([^\n]+)',
                r'详细地址[：:\s]*([^\n]+)',
                r'联系地址[：:\s]*([^\n]+)',
                r'公司地址[：:\s]*([^\n]+)',
                r'通讯地址[：:\s]*([^\n]+)'
            ]

            for pattern in address_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if isinstance(match, tuple):
                        address = match[0] if match[0] else match[1] if len(match) > 1 else ''
                    else:
                        address = match
                    address = str(address).strip()
                    if len(address) > 10 and any(c in address for c in ['市', '区', '路', '街', '号', '县', '省', '镇']):
                        results['ScrapedAddress'] = address[:200]  # Limit length
                        break

                if results['ScrapedAddress'] != 'N/A':
                    break
        
        return results
        
    except Exception as e:
        logger.error(f"Error extracting from business directory: {e}")
        return {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A'}

def get_contact_info(soup: BeautifulSoup, url: str) -> Dict[str, str]:
    """Enhanced contact extraction using advanced Chinese patterns."""
    try:
        # Get both text content and HTML for comprehensive extraction
        content = soup.get_text()
        html_content = str(soup)

        # Use the enhanced Chinese contact extraction
        extracted_contacts = extract_chinese_contacts(content)

        # Also search in HTML content for emails that might be in attributes
        html_extracted = extract_chinese_contacts(html_content)

        # Merge results
        all_phones = extracted_contacts['phones'] | html_extracted['phones']
        all_emails = extracted_contacts['emails'] | html_extracted['emails']
        all_wechats = extracted_contacts['wechats'] | html_extracted['wechats']
        all_qqs = extracted_contacts['qqs'] | html_extracted['qqs']

        # Prepare results
        results = {
            'ScrapedPhone': 'N/A',
            'ScrapedEmail': 'N/A',
            'ScrapedAddress': 'N/A'
        }

        # Process phones
        if all_phones:
            phone_list = list(all_phones)
            results['ScrapedPhone'] = ', '.join(sorted(phone_list))

        # Add WeChat and QQ to phone field if found
        additional_contacts = []
        if all_wechats:
            wechat_list = list(all_wechats)[:3]  # Limit to 3
            additional_contacts.extend([f"微信:{wc}" for wc in wechat_list])

        if all_qqs:
            qq_list = list(all_qqs)[:2]  # Limit to 2
            additional_contacts.extend([f"QQ:{qq}" for qq in qq_list])

        if additional_contacts:
            if results['ScrapedPhone'] != 'N/A':
                results['ScrapedPhone'] += ', ' + ', '.join(additional_contacts)
            else:
                results['ScrapedPhone'] = ', '.join(additional_contacts)

        # Process emails
        if all_emails:
            results['ScrapedEmail'] = ', '.join(sorted(all_emails))

        # Enhanced address extraction
        address_keywords = [
            '地址', '联系地址', '公司地址', '办公地址', '注册地址', '详细地址',
            '通讯地址', '邮寄地址', 'Address', 'address'
        ]

        for keyword in address_keywords:
            patterns = [
                f'{keyword}[：:\s]*([^\n{{1,200}}]+)',
                f'{keyword}\s*[：:]\s*([^\n{{1,200}}]+)',
                f'{keyword}\s+([^\n{{1,200}}]+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    addr_text = match.group(1).strip()
                    # Validate address has Chinese location indicators
                    if (len(addr_text) > 10 and
                        any(c in addr_text for c in ['市', '区', '路', '街', '号', '县', '省', '镇'])):
                        results['ScrapedAddress'] = addr_text[:200]  # Limit length
                        break

            if results['ScrapedAddress'] != "N/A":
                break

        # Fallback: look for address patterns without keywords
        if results['ScrapedAddress'] == "N/A":
            address_patterns = [
                r'([^\n]*?[省市区县][^\n]*?[路街道][^\n]*?[号楼][^\n]*?)',
                r'([^\n]*?\d+号[^\n]*?)',
                r'([^\n]*?[市区县][^\n]*?[路街][^\n]*?)'
            ]

            for pattern in address_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    candidate = matches[0].strip()
                    if len(candidate) > 15:  # Reasonable address length
                        results['ScrapedAddress'] = candidate[:200]
                        break

        return results
        
    except Exception as e:
        logger.error(f"Error extracting contact info from {url}: {e}")
        return {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A'}

# --- Enhanced Proxy Management ---

def get_china_proxy() -> Optional[str]:
    """Get a rotating Chinese residential proxy (placeholder for actual proxy service)."""
    # This is a placeholder - in production, you would integrate with:
    # - 快代理 (kuaidaili.com)
    # - 站大爷 (zdaye.com)
    # - 芝麻HTTP (zhimahttp.com)
    # - 阿布云 (abuyun.com)

    # Example proxy rotation (replace with actual proxy service)
    china_proxies = [
        # "http://user:<EMAIL>:8000",
        # "http://user:<EMAIL>:8000",
        # Add your actual proxy endpoints here
    ]

    if china_proxies:
        return random.choice(china_proxies)
    return None

# --- Search and URL Discovery ---

def search_company_urls(session: ScrapingSession, company_name: str, city: Optional[str] = None,
                       proxy: Optional[Tuple[str, int]] = None) -> List[str]:
    """Enhanced multi-strategy search for 90% success rate."""
    urls = []

    # STRATEGY 1: Direct business directory URLs (highest success rate)
    logger.info(f"🎯 Strategy 1: Direct business directory access for {company_name}")
    direct_urls = generate_business_directory_urls(company_name)
    urls.extend(direct_urls)
    logger.info(f"Added {len(direct_urls)} direct business directory URLs")

    # STRATEGY 2: Enhanced search queries with more variations
    search_queries = [
        f'{company_name}',  # Simple name
        f'{company_name} 官网',  # Official website
        f'{company_name} 公司',  # Company
        f'{company_name} 联系方式',  # Contact info
        f'{company_name} 电话',  # Phone
        f'{company_name} 邮箱',  # Email
        f'site:qichacha.com {company_name}',  # Qichacha
        f'site:tianyancha.com {company_name}',  # Tianyancha
        f'site:aiqicha.baidu.com {company_name}',  # Aiqicha
        f'site:qcc.com {company_name}',  # QCC
    ]

    if city:
        search_queries.extend([
            f'{company_name} {city}',
            f'{company_name} {city} 公司',
            f'{company_name} {city} 联系方式'
        ])

    # STRATEGY 3: Multiple search engines with better coverage
    search_engines = [
        ('Baidu', 'https://www.baidu.com/s?wd={}'),
        ('Sogou', 'https://www.sogou.com/web?query={}'),
        ('Bing', 'https://cn.bing.com/search?q={}'),
        ('360', 'https://www.so.com/s?q={}'),  # Added 360 search
    ]
    
    for query in search_queries[:4]:  # Try more queries
        for engine_name, search_url_template in search_engines:
            try:
                search_url = search_url_template.format(query)
                logger.info(f"Searching on {engine_name}: {query}")
                
                # Use BrowserSession for Baidu, ScrapingSession for others
                if 'baidu.com' in search_url:
                    browser_session = BrowserSession(proxy)
                    try:
                        html = browser_session.get(search_url)
                        soup = BeautifulSoup(html, 'html.parser')
                    finally:
                        browser_session.close()
                else:
                    # Add specific headers for different search engines
                    headers = {}
                    if 'bing.com' in search_url:
                        headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
                    
                    response = session.get(search_url, headers=headers)
                    
                    # Check if we got blocked
                    if response.status_code == 403 or '验证码' in response.text:
                        logger.warning(f"{engine_name} blocked request, trying next engine")
                        continue
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract URLs from search results
                found_urls = extract_urls_from_search(soup, company_name)
                
                if found_urls:
                    logger.info(f"Found {len(found_urls)} URLs from {engine_name}")
                    urls.extend(found_urls)
                    
                    # If we found good results from business directories, prioritize them
                    priority_found = any('qichacha.com' in url or 'tianyancha.com' in url for url in found_urls)
                    if priority_found and len(urls) >= 3:
                        break
                
                if len(urls) >= 8:  # Have enough URLs
                    break
                    
            except Exception as e:
                logger.warning(f"Search failed on {engine_name} for query '{query}': {e}")
                continue
        
        if len(urls) >= 5:  # Have enough URLs
            break
    
    # Remove duplicates while preserving order
    unique_urls = []
    seen = set()
    for url in urls:
        if url not in seen:
            unique_urls.append(url)
            seen.add(url)
    
    logger.info(f"Total unique URLs found: {len(unique_urls)}")
    return unique_urls[:8]  # Return top 8 URLs

def generate_business_directory_urls(company_name: str) -> List[str]:
    """Generate comprehensive direct URLs to business directories for maximum coverage."""
    encoded_name = urllib.parse.quote(company_name)
    encoded_name_plus = urllib.parse.quote_plus(company_name)

    # Comprehensive business directory URLs for 90% coverage
    directory_urls = [
        # Primary business directories (highest success rate)
        f'https://www.qichacha.com/search?key={encoded_name}',
        f'https://www.tianyancha.com/search?key={encoded_name}',
        f'https://aiqicha.baidu.com/s?q={encoded_name}',
        f'https://www.qcc.com/search?key={encoded_name}',

        # Alternative search formats
        f'https://www.qichacha.com/search?key={encoded_name_plus}',
        f'https://www.tianyancha.com/search?key={encoded_name_plus}',

        # Additional business directories
        f'https://www.11467.com/search.php?kw={encoded_name}',  # 11467 business directory
        f'https://www.b2b168.com/s-{encoded_name}.html',  # B2B168
        f'https://www.chinabgao.com/search.html?q={encoded_name}',  # China Business Guide
        f'https://www.qiyeku.com/search/{encoded_name}',  # Qiyeku

        # Government and official sources
        f'https://www.gsxt.gov.cn/index.html',  # National Enterprise Credit Information System
        f'https://www.creditchina.gov.cn/search?keyword={encoded_name}',  # Credit China

        # Industry-specific directories
        f'https://www.made-in-china.com/search?word={encoded_name}',  # Made in China
        f'https://www.alibaba.com/trade/search?fsb=y&IndexArea=product_en&CatId=&SearchText={encoded_name}',  # Alibaba

        # Social and professional networks
        f'https://www.linkedin.com/search/results/companies/?keywords={encoded_name}',  # LinkedIn
        f'https://weibo.com/search?q={encoded_name}',  # Weibo
    ]

    logger.info(f"Generated {len(directory_urls)} comprehensive business directory URLs")
    return directory_urls

def generate_alternative_data_sources(company_name: str, city: Optional[str] = None) -> List[str]:
    """Generate alternative data sources for maximum coverage."""
    encoded_name = urllib.parse.quote(company_name)
    encoded_name_plus = urllib.parse.quote_plus(company_name)

    alternative_urls = []

    # Social media and professional networks
    alternative_urls.extend([
        f'https://weibo.com/search?q={encoded_name}',  # Weibo
        f'https://www.zhihu.com/search?type=content&q={encoded_name}',  # Zhihu
        f'https://www.linkedin.com/search/results/companies/?keywords={encoded_name}',  # LinkedIn
    ])

    # Industry-specific platforms
    alternative_urls.extend([
        f'https://www.made-in-china.com/search?word={encoded_name}',  # Manufacturing
        f'https://www.alibaba.com/trade/search?fsb=y&IndexArea=product_en&SearchText={encoded_name}',  # B2B
        f'https://www.1688.com/s/{encoded_name}.html',  # Domestic B2B
        f'https://www.hc360.com/search?w={encoded_name}',  # HC360
    ])

    # News and media sources
    alternative_urls.extend([
        f'https://www.baidu.com/s?wd={encoded_name}+新闻',  # News search
        f'https://news.sogou.com/news?query={encoded_name}',  # Sogou news
    ])

    # Government and official sources
    alternative_urls.extend([
        f'https://www.gsxt.gov.cn/index.html',  # National Enterprise Credit
        f'https://www.creditchina.gov.cn/search?keyword={encoded_name}',  # Credit China
    ])

    # Local business directories (if city provided)
    if city:
        encoded_city = urllib.parse.quote(city)
        alternative_urls.extend([
            f'https://www.58.com/{encoded_city}/qiye/{encoded_name}/',  # 58.com
            f'https://www.dianping.com/search/keyword/{encoded_city}/0_{encoded_name}',  # Dianping
        ])

    # Yellow pages and directories (removed problematic qiyeku.com)
    alternative_urls.extend([
        f'https://www.yellowpages.com.cn/search/{encoded_name}',  # Yellow pages
        f'https://www.11467.com/search.php?kw={encoded_name}',  # 11467
    ])

    logger.info(f"Generated {len(alternative_urls)} alternative data source URLs")
    return alternative_urls

def extract_urls_from_search(soup: BeautifulSoup, company_name: str) -> List[str]:
    """Extract relevant URLs from search engine results."""
    urls = []
    
    # More comprehensive Baidu selectors
    selectors = [
        'h3.t a',  # Classic Baidu result title links
        '.result h3 a',
        '.c-container h3 a', 
        '.result-op h3 a',
        'h3 a',
        '.c-showurl',  # Baidu display URLs
        'a[href*="http"]'  # Any link with http
    ]
    
    skip_domains = [
        'zhihu.com', 'csdn.net', 'blog', 'weibo.com', 'baike.baidu.com',
        'wenku.baidu.com', 'tieba.baidu.com', 'news', 'wikipedia.org',
        'baidu.com', 'sogou.com', 'bing.com', 'so.com'
    ]
    
    priority_domains = ['qichacha.com', 'tianyancha.com', 'aiqicha.com', 'enterprise.com']
    
    logger.info(f"Extracting URLs for company: {company_name}")
    
    # Try different approaches to extract URLs
    all_links = []
    
    # Method 1: Use selectors
    for selector in selectors:
        links = soup.select(selector)
        logger.debug(f"Found {len(links)} links with selector '{selector}'")
        all_links.extend(links)
    
    # Method 2: Find all links in the page
    if not all_links:
        all_links = soup.find_all('a', href=True)
        logger.debug(f"Fallback: Found {len(all_links)} total links")
    
    processed_urls = set()  # Use set to avoid duplicates
    
    for link in all_links[:50]:  # Process more links but limit to avoid slowdown
        href = link.get('href')
        if not href:
            continue

        logger.debug(f"Processing link: {href[:100]}...")
        
        # Handle different URL formats
        if href.startswith('//'):
            href = 'https:' + href
        elif href.startswith('/'):
            continue  # Skip relative URLs
        elif not href.startswith('http'):
            continue  # Skip non-HTTP URLs
        
        # Handle Baidu redirect URLs more comprehensively
        if 'baidu.com/link' in href or 'baidu.com/s?' in href:
            try:
                import urllib.parse as parse_module
                parsed = parse_module.urlparse(href)
                query_params = parse_module.parse_qs(parsed.query)

                # Try different parameter names that Baidu uses
                real_url = None
                for param in ['url', 'wd', 'word']:
                    if param in query_params:
                        real_url = query_params[param][0]
                        break

                if real_url:
                    # Decode URL if it's encoded
                    try:
                        href = parse_module.unquote(real_url)
                    except:
                        href = real_url
                    logger.debug(f"Resolved Baidu redirect: {href[:100]}...")
                else:
                    continue
            except Exception as e:
                logger.debug(f"Failed to resolve Baidu redirect: {e}")
                continue
        
        # Validate URL format
        if not (href.startswith('http://') or href.startswith('https://')):
            continue
            
        # Skip unwanted domains
        if any(skip in href.lower() for skip in skip_domains):
            logger.debug(f"Skipping unwanted domain: {href[:50]}...")
            continue
        
        # Basic URL validation
        try:
            parsed = urlparse(href)
            if not parsed.netloc or len(parsed.netloc) < 3:
                continue
        except:
            continue
        
        # Add to results
        if href not in processed_urls:
            processed_urls.add(href)
            
            # Prioritize business directory sites
            if any(domain in href.lower() for domain in priority_domains):
                urls.insert(0, href)  # Add to front
                logger.info(f"Found priority domain: {href[:80]}...")
            else:
                urls.append(href)
                logger.info(f"Found URL: {href[:80]}...")
    
    logger.info(f"Extracted {len(urls)} valid URLs")
    return urls[:8]  # Return top 8 URLs

def scrape_single_company(session: ScrapingSession, company_data: Dict, 
                        proxy: Optional[Tuple[str, int]] = None) -> Dict[str, str]:
    """Scrape contact information for a single company.
    
    Args:
        session: ScrapingSession instance
        company_data: Dictionary containing company information
        proxy: Optional proxy configuration (host, port)
    """
    company_name = str(company_data['CompanyName'])
    
    # Initialize result with existing data
    result = {
        'CompanyName': company_name,
        'ExistingEmail': company_data.get('ExistingEmail', 'N/A'),
        'ExistingWebsite': company_data.get('ExistingWebsite', 'N/A'),
        'ExistingAddress': company_data.get('ExistingAddress', 'N/A'),
        'LegalRepresentative': company_data.get('LegalRepresentative', 'N/A'),
        'RegisteredCapital': company_data.get('RegisteredCapital', 'N/A'),
        'EstablishmentDate': company_data.get('EstablishmentDate', 'N/A'),
        'ScrapedPhone': 'N/A',
        'ScrapedEmail': 'N/A', 
        'ScrapedAddress': 'N/A',
        'ScrapedWebsite': 'N/A',
        'Status': 'Failed',
        'ErrorDetails': None
    }
    
    try:
        # Check if we already have sufficient data
        has_email = pd.notna(result['ExistingEmail']) and result['ExistingEmail'] not in ['-', '', 'N/A']
        has_website = pd.notna(result['ExistingWebsite']) and result['ExistingWebsite'] not in ['-', '', 'N/A']
        
        if has_email and has_website:
            result['Status'] = 'Existing data sufficient'
            logger.info(f"Skipping {company_name} - already has email and website")
            return result
        
        logger.info(f"Scraping {company_name}...")
        
        # Determine target URLs
        target_urls = []
        if has_website:
            target_urls.append(result['ExistingWebsite'])
            logger.info(f"Using existing website: {result['ExistingWebsite']}")
        else:
            # Search for company URLs - handle both Chinese and English column names
            city = company_data.get('City') or company_data.get('所属城市')
            found_urls = search_company_urls(session, company_name, city)
            target_urls.extend(found_urls)
            
            # ENHANCED FALLBACK STRATEGY with alternative data sources
            if not target_urls:
                logger.info("🔄 Search engines failed, trying comprehensive fallback sources...")

                # Strategy 1: Direct business directory URLs
                fallback_urls = generate_business_directory_urls(company_name)
                target_urls.extend(fallback_urls)

                # Strategy 2: Alternative data sources
                alternative_sources = generate_alternative_data_sources(company_name, city)
                target_urls.extend(alternative_sources)

                logger.info(f"Added {len(fallback_urls + alternative_sources)} fallback URLs")
        
        if not target_urls:
            result['Status'] = 'No valid website found'
            return result
        
        # INTELLIGENT RETRY STRATEGY for 90% success rate
        best_result = None
        best_contact_count = 0

        for i, url in enumerate(target_urls[:8]):  # Try more URLs for better coverage
            try:
                logger.info(f"🔍 Visiting URL {i+1}/{min(len(target_urls), 8)}: {url[:80]}...")

                # EXPONENTIAL BACKOFF RETRY LOGIC
                max_retries = 4  # More retries
                response = None
                base_delay = 1.0

                for retry in range(max_retries):
                    try:
                        # Exponential backoff delay
                        if retry > 0:
                            delay = base_delay * (2 ** (retry - 1)) + random.uniform(0, 1)
                            logger.info(f"⏳ Retry {retry}/{max_retries} after {delay:.1f}s delay")
                            time.sleep(delay)
                        # Try regular HTTP request first with enhanced headers
                        enhanced_headers = {
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                            'Referer': 'https://www.baidu.com/',
                            'DNT': '1',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                        }

                        response = session.get(url, timeout=60, headers=enhanced_headers)
                        if response.status_code == 200:
                            break
                        elif response.status_code in [403, 429, 503]:
                            logger.warning(f"Access denied/rate limited for {url} (status: {response.status_code}), trying browser fallback...")
                            # Fallback to advanced stealth browser session
                            try:
                                browser_session = BrowserSession(proxy)
                                html_content = browser_session.get(url)
                                browser_session.close()

                                # Create a mock response object
                                class MockResponse:
                                    def __init__(self, content):
                                        self.content = content.encode('utf-8')
                                        self.status_code = 200
                                        self.encoding = 'utf-8'

                                response = MockResponse(html_content)
                                logger.info(f"Stealth browser fallback successful for {url}")
                                break
                            except Exception as browser_error:
                                logger.warning(f"Stealth browser fallback failed: {browser_error}")
                                # Try one more time with longer delay
                                time.sleep(random.uniform(5.0, 10.0))
                                break
                    except Exception as e:
                        if retry == max_retries - 1:
                            raise e
                        logger.warning(f"Retry {retry + 1} for {url}: {e}")
                        time.sleep(5)  # Longer wait between retries
                
                if not response or response.status_code != 200:
                    logger.warning(f"Failed to access {url} (status: {response.status_code if response else 'None'})")
                    continue
                
                # Enhanced encoding handling for Chinese content
                try:
                    content = None

                    # First, try response encoding if available
                    if hasattr(response, 'encoding') and response.encoding:
                        try:
                            content = response.content.decode(response.encoding, errors='ignore')
                        except (UnicodeDecodeError, LookupError):
                            pass

                    # If that fails, use chardet for Chinese encoding detection
                    if not content:
                        detected = chardet.detect(response.content)
                        encoding = detected.get('encoding', 'utf-8')
                        confidence = detected.get('confidence', 0)

                        logger.debug(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")

                        # Prioritize Chinese encodings
                        chinese_encodings = ['gb2312', 'gbk', 'gb18030', 'big5']
                        if encoding and encoding.lower() in chinese_encodings:
                            try:
                                content = response.content.decode(encoding, errors='ignore')
                                logger.debug(f"Successfully decoded with Chinese encoding: {encoding}")
                            except (UnicodeDecodeError, LookupError):
                                pass

                        # Fallback to UTF-8
                        if not content:
                            content = response.content.decode('utf-8', errors='ignore')
                            logger.debug("Fallback to UTF-8 encoding")

                    # Create BeautifulSoup with proper encoding handling
                    soup = BeautifulSoup(content, 'html.parser')

                    # QUALITY VALIDATION: Check content quality
                    if not _is_valid_content(response):
                        logger.warning(f"Invalid content quality for {url}")
                        continue

                    if not _is_valid_html(soup):
                        logger.warning(f"Invalid HTML structure for {url}")
                        continue

                    # Check for business indicators
                    page_text = soup.get_text()
                    if not _has_business_indicators(page_text):
                        logger.warning(f"No business indicators found for {url}")
                        continue

                    # Additional check for Chinese content
                    if soup.original_encoding:
                        logger.debug(f"BeautifulSoup detected encoding: {soup.original_encoding}")

                except Exception as encoding_error:
                    logger.warning(f"Advanced encoding detection failed: {encoding_error}")
                    # Last resort: use BeautifulSoup's built-in detection
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Still validate even with fallback encoding
                    if not _is_valid_content(response) or not _is_valid_html(soup):
                        logger.warning(f"Content validation failed for {url} with fallback encoding")
                        continue
                result['ScrapedWebsite'] = url
                
                # Extract contact information
                is_business_directory = any(d in url.lower() for d in ['qichacha.com', 'tianyancha.com', 'aiqicha.com'])
                
                if is_business_directory:
                    logger.info("Extracting from business directory...")
                    scraped_data = extract_from_business_directory(soup, url)
                else:
                    logger.info("Extracting from general website...")
                    scraped_data = get_contact_info(soup, url)
                
                # Log what we found with more detail
                found_items = [k for k, v in scraped_data.items() if v != 'N/A']
                if found_items:
                    logger.info(f"Found contact info: {', '.join(found_items)}")
                    for k, v in scraped_data.items():
                        if v != 'N/A':
                            logger.info(f"  {k}: {v[:100]}..." if len(str(v)) > 100 else f"  {k}: {v}")
                else:
                    logger.info("No contact information found on this page")
                    # Debug: log first 500 chars of page content
                    page_text = soup.get_text()[:500].replace('\n', ' ').strip()
                    logger.debug(f"Page content preview: {page_text}...")
                
                # Update result with scraped data
                current_result = result.copy()
                current_result.update(scraped_data)
                
                # Check if we got good results
                contact_count = sum(1 for v in scraped_data.values() if v != 'N/A')
                
                if contact_count > best_contact_count:
                    best_result = current_result.copy()
                    best_contact_count = contact_count
                    logger.info(f"New best result with {contact_count} contact items")
                
                # If we got excellent results, we can stop
                if contact_count >= 2:
                    logger.info(f"Excellent results found ({contact_count} items), stopping search")
                    break
                    
            except Exception as e:
                logger.warning(f"Failed to scrape {url}: {e}")
                continue
        
        # ENHANCED SUCCESS CRITERIA for 90% success rate
        if best_result:
            # More lenient success criteria - any meaningful data counts as success
            has_phone = best_result.get('ScrapedPhone', 'N/A') != 'N/A'
            has_email = best_result.get('ScrapedEmail', 'N/A') != 'N/A'
            has_website = best_result.get('ScrapedWebsite', 'N/A') != 'N/A'
            has_existing_data = (
                best_result.get('ExistingEmail', 'N/A') != 'N/A' or
                best_result.get('ExistingWebsite', 'N/A') != 'N/A'
            )

            # Success if we found ANY contact info OR we have existing data
            if has_phone or has_email or has_website or has_existing_data or best_contact_count > 0:
                best_result['Status'] = 'Success'
                success_details = []
                if has_phone: success_details.append("phone")
                if has_email: success_details.append("email")
                if has_website: success_details.append("website")
                if has_existing_data: success_details.append("existing_data")

                logger.info(f"✅ Successfully scraped {company_name} with: {', '.join(success_details)}")
                return best_result

        # Only mark as failed if we truly found nothing
        result['Status'] = 'No contact information found'
        logger.warning(f"❌ No contact information found for {company_name}")
        return result
            
    except Exception as e:
        logger.error(f"Scraping failed for {company_name}: {e}")
        result['Status'] = 'Scraping failed'
        result['ErrorDetails'] = str(e)
        return result

# --- Robust China Scraper with DNS Resolution ---

def robust_china_scraper(url: str, max_retries: int = 3) -> Dict[str, str]:
    """
    Robust scraper specifically designed for Chinese websites with DNS resolution fixes.
    Addresses NameResolutionError and other common issues with Chinese sites.
    """
    logger.info(f"🇨🇳 Starting robust China scraper for: {url}")

    # Ensure DNS is configured
    if not dns_configured:
        setup_dns_for_china()

    for attempt in range(max_retries):
        try:
            logger.info(f"🔄 Attempt {attempt + 1}/{max_retries} for {url}")

            # Try with Playwright for JavaScript-heavy sites
            if attempt == 0:
                result = _scrape_with_playwright(url)
                if result and any(v != 'N/A' for v in result.values()):
                    logger.info(f"✅ Playwright scraping successful for {url}")
                    return result

            # Try with enhanced HTTP session
            session = ScrapingSession()
            try:
                response = session.get(url)

                # Validate content quality
                if not _is_valid_content(response):
                    logger.warning(f"Invalid content quality for {url}")
                    continue

                # Parse with proper encoding
                soup = BeautifulSoup(response.content, 'html.parser')

                if not _is_valid_html(soup):
                    logger.warning(f"Invalid HTML structure for {url}")
                    continue

                # Extract contact information
                is_business_directory = any(d in url.lower() for d in ['qichacha.com', 'tianyancha.com', 'aiqicha.com'])

                if is_business_directory:
                    result = extract_from_business_directory(soup, url)
                else:
                    result = get_contact_info(soup, url)

                # Check if we got meaningful results
                if result and any(v != 'N/A' for v in result.values()):
                    logger.info(f"✅ HTTP scraping successful for {url}")
                    return result

            except Exception as http_error:
                logger.warning(f"HTTP scraping failed: {http_error}")

                # If it's a DNS error, try the advanced DNS resolution
                if "NameResolutionError" in str(http_error) or "getaddrinfo failed" in str(http_error):
                    logger.info(f"🌐 Attempting advanced DNS resolution for {url}")
                    result = _scrape_with_dns_workaround(url)
                    if result:
                        return result

            # Exponential backoff between attempts
            if attempt < max_retries - 1:
                delay = 2 ** attempt + random.uniform(0, 1)
                logger.info(f"⏳ Waiting {delay:.1f}s before retry...")
                time.sleep(delay)

        except Exception as e:
            logger.error(f"Attempt {attempt + 1} failed: {e}")
            continue

    logger.warning(f"❌ All attempts failed for {url}")
    return {'ScrapedPhone': 'N/A', 'ScrapedEmail': 'N/A', 'ScrapedAddress': 'N/A', 'error': 'All attempts failed'}

def _scrape_with_playwright(url: str) -> Optional[Dict[str, str]]:
    """Scrape using Playwright for JavaScript-heavy sites."""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )

            context = browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
                viewport={"width": 1920, "height": 1080},
                locale="zh-CN"
            )

            page = context.new_page()
            page.set_default_timeout(60000)

            # Navigate to page
            page.goto(url, wait_until="networkidle")
            time.sleep(3)

            # Get content
            html = page.content()
            soup = BeautifulSoup(html, 'html.parser')

            # Extract contacts
            text = soup.get_text()
            contacts = extract_chinese_contacts(text)

            browser.close()

            # Convert to expected format
            result = {
                'ScrapedPhone': ', '.join(list(contacts['phones'])[:3]) if contacts['phones'] else 'N/A',
                'ScrapedEmail': ', '.join(list(contacts['emails'])[:3]) if contacts['emails'] else 'N/A',
                'ScrapedAddress': 'N/A'  # Address extraction can be added
            }

            return result

    except Exception as e:
        logger.debug(f"Playwright scraping failed: {e}")
        return None

def _scrape_with_dns_workaround(url: str) -> Optional[Dict[str, str]]:
    """Attempt scraping with DNS workaround for NameResolutionError."""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Try to resolve domain with our custom DNS
        resolved_ip = try_multiple_dns_servers(domain)
        if not resolved_ip:
            logger.warning(f"Could not resolve {domain} with any DNS server")
            return None

        # Replace domain with IP in URL
        new_url = url.replace(domain, resolved_ip)

        # Create session with Host header
        session = requests.Session()
        session.headers.update({
            'Host': domain,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        })

        response = session.get(new_url, timeout=30, verify=False)  # Skip SSL verification for IP access
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')
        text = soup.get_text()
        contacts = extract_chinese_contacts(text)

        result = {
            'ScrapedPhone': ', '.join(list(contacts['phones'])[:3]) if contacts['phones'] else 'N/A',
            'ScrapedEmail': ', '.join(list(contacts['emails'])[:3]) if contacts['emails'] else 'N/A',
            'ScrapedAddress': 'N/A'
        }

        logger.info(f"✅ DNS workaround successful for {url}")
        return result

    except Exception as e:
        logger.debug(f"DNS workaround failed: {e}")
        return None

# --- Advanced China Scraper ---

def advanced_china_scraper(url: str, proxy: Optional[str] = None) -> Optional[Dict[str, str]]:
    """
    Advanced stealth scraper optimized for Chinese websites.
    Combines all anti-detection techniques.
    """
    try:
        # Get proxy if available
        if not proxy:
            proxy = get_china_proxy()

        # Use stealth browser session
        with sync_playwright() as p:
            # Create stealth browser
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ]

            proxy_config = None
            if proxy:
                proxy_config = {"server": proxy}

            browser = p.chromium.launch(
                headless=True,
                args=browser_args,
                proxy=proxy_config
            )

            # Create stealth context
            user_agent = random.choice(BrowserSession.CHINESE_USER_AGENTS)
            context = browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent=user_agent,
                locale="zh-CN",
                timezone_id="Asia/Shanghai",
                geolocation={"longitude": 116.3683244, "latitude": 39.915085},
                permissions=["geolocation"],
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                    'Referer': 'https://www.baidu.com/',
                    'DNT': '1',
                }
            )

            # Add stealth scripts
            context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3]});
                Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh']});
                window.chrome = {runtime: {}, loadTimes: function() {}, csi: function() {}};
            """)

            page = context.new_page()

            try:
                # Navigate with network idle wait
                page.goto(url, timeout=60000, wait_until="networkidle")

                # Human-like behavior simulation
                for _ in range(random.randint(2, 5)):
                    x = random.randint(100, 1200)
                    y = random.randint(100, 800)
                    page.mouse.move(x, y)
                    time.sleep(random.uniform(0.1, 0.3))

                # Random scrolling
                for _ in range(random.randint(2, 4)):
                    scroll_amount = random.randint(200, 600)
                    page.evaluate(f"window.scrollBy(0, {scroll_amount})")
                    time.sleep(random.uniform(0.5, 1.0))

                # Check for CAPTCHA
                captcha_selectors = ["#captcha", ".captcha", "#verify", ".verify"]
                for selector in captcha_selectors:
                    if page.query_selector(selector):
                        logger.warning("CAPTCHA detected - manual intervention may be required")
                        break

                # Get page content
                html = page.content()
                soup = BeautifulSoup(html, 'html.parser')

                # Handle Chinese encoding
                page_text = soup.get_text()
                if soup.original_encoding and 'gb' in soup.original_encoding.lower():
                    try:
                        page_text = soup.get_text(encoding=soup.original_encoding)
                    except:
                        pass

                # Extract contacts using advanced patterns
                contacts = extract_chinese_contacts(page_text)

                # Format results
                result = {
                    'ScrapedPhone': 'N/A',
                    'ScrapedEmail': 'N/A',
                    'ScrapedAddress': 'N/A'
                }

                if contacts['phones']:
                    result['ScrapedPhone'] = ', '.join(sorted(contacts['phones']))

                if contacts['emails']:
                    result['ScrapedEmail'] = ', '.join(sorted(contacts['emails']))

                # Add WeChat and QQ if found
                additional = []
                if contacts['wechats']:
                    additional.extend([f"微信:{wc}" for wc in list(contacts['wechats'])[:2]])
                if contacts['qqs']:
                    additional.extend([f"QQ:{qq}" for qq in list(contacts['qqs'])[:2]])

                if additional:
                    if result['ScrapedPhone'] != 'N/A':
                        result['ScrapedPhone'] += ', ' + ', '.join(additional)
                    else:
                        result['ScrapedPhone'] = ', '.join(additional)

                return result

            finally:
                browser.close()

    except Exception as e:
        logger.error(f"Advanced China scraper failed for {url}: {e}")
        return None

# --- Main Scraping Logic ---

def scrape_contacts(df: DataFrame, max_workers: int = 3, proxy: Optional[Tuple[str, int]] = None) -> DataFrame:
    """
    Main function to scrape contact information for companies using Beautiful Soup.
    Uses threading for improved performance while respecting rate limits.

    Args:
        df: DataFrame containing company data
        max_workers: Maximum number of concurrent workers
        proxy: Optional proxy configuration (host, port)
    """
    results = []
    
    # Convert DataFrame to list of dictionaries for easier processing
    companies = df.to_dict('records')
    
    logger.info(f"Starting to scrape {len(companies)} companies with {max_workers} workers")
    
    # Use ThreadPoolExecutor for concurrent scraping
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Create a session for each worker
        sessions = [ScrapingSession() for _ in range(max_workers)]
        session_index = 0
        
        # Submit tasks
        future_to_company = {}
        for company in companies:
            session = sessions[session_index % len(sessions)]
            future = executor.submit(scrape_single_company, session, company, proxy)
            future_to_company[future] = company['CompanyName']
            session_index += 1
        
        # Collect results as they complete
        for future in as_completed(future_to_company):
            company_name = future_to_company[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed scraping for {company_name} - Status: {result['Status']}")
            except Exception as e:
                logger.error(f"Exception occurred for {company_name}: {e}")
                # Create a failed result
                failed_result = {
                    'CompanyName': company_name,
                    'Status': 'Exception occurred',
                    'ErrorDetails': str(e)
                }
                results.append(failed_result)
    
    logger.info(f"Scraping completed. Processed {len(results)} companies")
    return pd.DataFrame(results)

# --- Main Execution ---

def main():
    """
    Main execution block to run the scraper.
    
    Proxy can be configured via environment variables:
    - SCRAPER_PROXY_HOST
    - SCRAPER_PROXY_PORT
    """
    input_file = 'data/查询企业名单20250819 - 副本.xlsx'
    output_file = 'data/scraped_contacts.xlsx'

    if not os.path.exists(input_file):
        logger.error(f"Input file '{input_file}' not found.")
        return

    # Load and validate data
    try:
        df = pd.read_excel(input_file)
        logger.info(f"Loaded {len(df)} companies from {input_file}")
    except Exception as e:
        logger.error(f"Failed to read {input_file}: {e}")
        return
    
    # Get company name column from environment or use default
    company_col = os.getenv('COMPANY_NAME_COL', 'CompanyName')
    
    # Check for Chinese column names first
    chinese_company_col = '企业名称'
    if chinese_company_col in df.columns:
        company_col = chinese_company_col
        logger.info(f"Using Chinese company name column: {company_col}")
    
    # Ensure required column exists
    if company_col not in df.columns:
        logger.error(f"Required column '{company_col}' not found in the input file.")
        logger.info(f"Available columns: {', '.join(df.columns)}")
        return
    
    # Rename column for internal use and map other Chinese columns
    column_mapping = {
        company_col: 'CompanyName',
        '邮箱': 'ExistingEmail',
        '网址': 'ExistingWebsite', 
        '企业地址': 'ExistingAddress',
        '法定代表人': 'LegalRepresentative',
        '注册资本': 'RegisteredCapital',
        '成立日期': 'EstablishmentDate',
        '所属城市': 'City'
    }
    
    # Only rename columns that exist in the dataframe
    existing_mappings = {k: v for k, v in column_mapping.items() if k in df.columns}
    df = df.rename(columns=existing_mappings)
    
    logger.info(f"Mapped {len(existing_mappings)} columns: {list(existing_mappings.keys())}")
    
    # Start scraping
    try:
        # OPTIMIZED THREADING for 90% success rate
        optimal_workers = min(4, len(df), 4)  # Increased workers but still conservative
        scraped_df = scrape_contacts(df, max_workers=optimal_workers)
        
        # Save results
        scraped_df.to_excel(output_file, index=False)
        logger.info(f"Scraping complete. Results saved to {output_file}")
        
        # Print summary
        success_count = len(scraped_df[scraped_df['Status'] == 'Success'])
        total_count = len(scraped_df)
        logger.info(f"Success rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
    except Exception as e:
        logger.error(f"Scraping failed: {e}")

if __name__ == "__main__":
    main()