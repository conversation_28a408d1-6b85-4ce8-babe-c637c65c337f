import streamlit as st
import pandas as pd
import time
from utils import read_excel_file, save_to_excel
from scraper import scrape_contacts

st.set_page_config(page_title="Enterprise Contact Scraper", layout="wide")

st.title("🏢 Enterprise Contact Scraper")
st.markdown("Enhance your enterprise data with additional contact information")

# Sidebar for configuration
st.sidebar.header("Configuration")
max_companies = st.sidebar.slider("Max companies to scrape", 1, 50, 10)
max_workers = st.sidebar.slider("Concurrent workers", 1, 5, 3)
headless_mode = st.sidebar.checkbox("Run in headless mode", value=True)
use_browser_fallback = st.sidebar.checkbox("Use browser for difficult sites", value=True)

# Advanced settings
with st.sidebar.expander("Advanced Settings"):
    timeout_seconds = st.slider("Request timeout (seconds)", 30, 120, 60)
    retry_attempts = st.slider("Retry attempts per URL", 1, 5, 2)
    enable_chinese_optimization = st.checkbox("Chinese site optimization", value=True)

uploaded_file = st.file_uploader("Choose an Excel file", type=["xlsx", "xls"])

if uploaded_file is not None:
    try:
        df = read_excel_file(uploaded_file)
        st.success(f"File uploaded successfully! Found {len(df)} companies.")
        
        # Show data preview
        with st.expander("Preview uploaded data", expanded=True):
            st.dataframe(df.head())
        
        # Show existing contact info summary
        existing_emails = df['ExistingEmail'].notna().sum() if 'ExistingEmail' in df.columns else 0
        existing_websites = df['ExistingWebsite'].notna().sum() if 'ExistingWebsite' in df.columns else 0
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Companies", len(df))
        with col2:
            st.metric("Existing Emails", existing_emails)
        with col3:
            st.metric("Existing Websites", existing_websites)

        # Limit the number of companies to process
        df_limited = df.head(max_companies)
        
        if st.button("🚀 Start Scraping", use_container_width=True, type="primary"):
            progress_bar = st.progress(0)
            status_text = st.empty()
            results_container = st.empty()
            
            # Show scraping configuration
            st.info(f"🔧 Configuration: {max_workers} workers, {timeout_seconds}s timeout, Chinese optimization: {'✓' if enable_chinese_optimization else '✗'}")
            
            with st.spinner(f'Scraping contact information for {len(df_limited)} companies...'):
                # Use improved scraper with enhanced settings
                actual_workers = min(max_workers, len(df_limited))
                results_df = scrape_contacts(df_limited, max_workers=actual_workers)
                
                # Show real-time progress updates
                if len(results_df) > 0:
                    completed = len(results_df)
                    progress_bar.progress(min(completed / len(df_limited), 1.0))
                    status_text.text(f"Processed {completed}/{len(df_limited)} companies...")
                
            progress_bar.progress(100)
            status_text.text("Scraping completed!")
            
            st.success('✅ Scraping completed!')
            
            # Enhanced results summary
            success_count = len(results_df[results_df['Status'] == 'Success'])
            phone_found = len(results_df[results_df['ScrapedPhone'] != 'N/A'])
            email_found = len(results_df[results_df['ScrapedEmail'] != 'N/A'])
            address_found = len(results_df[results_df['ScrapedAddress'] != 'N/A'])
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Successfully Scraped", success_count)
            with col2:
                st.metric("Success Rate", f"{success_count/len(results_df)*100:.1f}%")
            with col3:
                st.metric("Phones Found", phone_found)
            with col4:
                st.metric("Emails Found", email_found)
            
            # Show detailed breakdown
            with st.expander("📊 Detailed Results Breakdown"):
                status_counts = results_df['Status'].value_counts()
                st.write("**Status Distribution:**")
                for status, count in status_counts.items():
                    st.write(f"- {status}: {count} companies")
                
                # Show sample successful results
                successful_results = results_df[results_df['Status'] == 'Success']
                if len(successful_results) > 0:
                    st.write("**Sample Successful Results:**")
                    sample_cols = ['CompanyName', 'ScrapedPhone', 'ScrapedEmail', 'ScrapedWebsite']
                    available_cols = [col for col in sample_cols if col in successful_results.columns]
                    st.dataframe(successful_results[available_cols].head(3))
            
            # Show results
            with st.expander("View scraped results", expanded=True):
                st.dataframe(results_df)

            # Download button
            excel_data = save_to_excel(results_df)
            st.download_button(
                label="📥 Download Scraped Data",
                data=excel_data,
                file_name=f"scraped_contacts_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.ms-excel",
                use_container_width=True
            )

    except Exception as e:
        st.error(f"❌ An error occurred: {e}")
        
        # Show troubleshooting tips
        with st.expander("🔧 Troubleshooting Tips"):
            st.markdown("""
            **Common Issues & Solutions:**
            
            1. **File Format Issues:**
               - Ensure your Excel file has company names in 'CompanyName' or '企业名称' column
               - Check that the file is not corrupted
            
            2. **Slow Scraping:**
               - Reduce the number of concurrent workers
               - Increase timeout settings for slow sites
            
            3. **No Results Found:**
               - Enable Chinese site optimization
               - Try reducing the batch size
               - Check if company names are spelled correctly
            
            4. **Access Denied Errors:**
               - Enable browser fallback mode
               - Reduce scraping speed (fewer workers)
            """)
        
else:
    st.info("👆 Upload an Excel file to get started. The file should contain company names in Chinese (企业名称) or English (CompanyName).")
    
    # Show feature highlights
    st.markdown("### 🚀 Enhanced Features for Chinese Sites")
    
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("""
        **Improved Chinese Site Support:**
        - ✅ Extended 60-second timeouts
        - ✅ Enhanced phone number patterns
        - ✅ Better viewport configuration (1920x1080)
        - ✅ Improved JavaScript rendering wait
        - ✅ Chinese business directory optimization
        """)
    
    with col2:
        st.markdown("""
        **Smart Extraction Features:**
        - 📞 Chinese mobile & landline numbers
        - 📧 Email addresses with validation
        - 🏢 Business addresses with location markers
        - 🌐 Website URLs from search results
        - 🔄 Automatic retry with fallback strategies
        """)
    
    # Show supported phone formats
    with st.expander("📱 Supported Phone Number Formats"):
        st.code("""
        ✅ Mobile: 138-1234-5678, +86 139 8765 4321, ***********
        ✅ Landline: 021-12345678, 010-87654321, 0755-12345678
        ✅ Service: ************, ************
        ✅ International: +86 138 1234 5678
        """)